{"format_version": "1.21.20", "minecraft:processor_list": {"description": {"identifier": "rza:abandoned_house_designer"}, "processors": [{"processor_type": "minecraft:rule", "rules": [{"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:cobblestone", "probability": 0.1}, "output_state": {"name": "minecraft:cracked_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:cobblestone", "probability": 0.1}, "output_state": {"name": "minecraft:mossy_cobblestone"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:cobblestone", "probability": 0.1}, "output_state": {"name": "minecraft:mossy_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:glass_pane", "probability": 0.2}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:glass_pane", "probability": 0.2}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:grass_path", "probability": 0.03}, "output_state": {"name": "minecraft:gravel"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:grass_path", "probability": 0.3}, "output_state": {"name": "minecraft:dirt"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:normal_stone_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:normal_stone_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_brick_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_brick_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_log", "probability": 0.02}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_log", "probability": 0.05}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_planks", "probability": 0.03}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_planks", "probability": 0.03}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_slab", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_slab", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_stairs", "probability": 0.01}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_fence", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_planks", "probability": 0.03}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_planks", "probability": 0.03}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_slab", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_slab", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_stairs", "probability": 0.05}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_stairs", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:cobblestone_wall", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:cobblestone_wall", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_bricks", "probability": 0.1}, "output_state": {"name": "minecraft:cracked_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_bricks", "probability": 0.1}, "output_state": {"name": "minecraft:mossy_cobblestone"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_bricks", "probability": 0.1}, "output_state": {"name": "minecraft:mossy_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_bricks", "probability": 0.1}, "output_state": {"name": "minecraft:mossy_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:stone_bricks", "probability": 0.15}, "output_state": {"name": "minecraft:cracked_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:wooden_door", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:wooden_door", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_slab", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:oak_slab", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_slab", "probability": 0.1}, "output_state": {"name": "minecraft:air"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:spruce_slab", "probability": 0.1}, "output_state": {"name": "minecraft:web"}}]}]}}