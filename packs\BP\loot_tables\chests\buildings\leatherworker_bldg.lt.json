{"pools": [{"rolls": {"min": 3, "max": 8}, "entries": [{"type": "empty", "weight": 40}, {"type": "item", "name": "minecraft:leather", "weight": 20, "functions": [{"function": "set_count", "count": {"min": 2, "max": 8}}]}, {"type": "item", "name": "minecraft:leather_helmet", "weight": 10, "functions": [{"function": "enchant_randomly", "chance": 0.3}]}, {"type": "item", "name": "minecraft:leather_chestplate", "weight": 10, "functions": [{"function": "enchant_randomly", "chance": 0.3}]}, {"type": "item", "name": "minecraft:leather_leggings", "weight": 10, "functions": [{"function": "enchant_randomly", "chance": 0.3}]}, {"type": "item", "name": "minecraft:leather_boots", "weight": 10, "functions": [{"function": "enchant_randomly", "chance": 0.3}]}, {"type": "item", "name": "minecraft:rabbit_hide", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 1, "max": 6}}]}, {"type": "item", "name": "minecraft:string", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 1, "max": 6}}]}, {"type": "item", "name": "minecraft:shears", "weight": 5}, {"type": "item", "name": "minecraft:emerald", "weight": 5, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}, {"type": "item", "name": "minecraft:saddle", "weight": 3}, {"type": "item", "name": "minecraft:frame", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:dye", "weight": 15, "functions": [{"function": "set_count", "count": {"min": 1, "max": 6}}, {"function": "set_data", "data": {"min": 0, "max": 15}}]}, {"type": "item", "name": "minecraft:lead", "weight": 8, "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}]}, {"type": "item", "name": "minecraft:cauldron", "weight": 3}, {"type": "item", "name": "minecraft:leather_horse_armor", "weight": 5}, {"type": "item", "name": "minecraft:book", "weight": 10, "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}]}]}]}