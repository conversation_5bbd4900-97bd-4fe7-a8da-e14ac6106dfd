{"type": "minecraft:chest", "pools": [{"rolls": {"min": 0, "max": 7}, "entries": [{"type": "empty", "weight": 100}, {"type": "item", "name": "rza:turret_base", "weight": 3}, {"type": "item", "name": "rza:arrow_turret_item", "weight": 3}]}, {"rolls": {"min": 0, "max": 5}, "entries": [{"type": "empty", "weight": 100}, {"type": "item", "name": "minecraft:book", "weight": 8, "functions": [{"function": "enchant_randomly"}, {"function": "enchant_randomly"}]}, {"type": "item", "name": "minecraft:golden_apple", "weight": 5}, {"type": "item", "name": "minecraft:appleEnchanted", "weight": 2}, {"type": "item", "name": "minecraft:name_tag", "weight": 4}, {"type": "item", "name": "minecraft:lead", "weight": 4}, {"type": "item", "name": "minecraft:ender_pearl", "weight": 3}, {"type": "item", "name": "minecraft:echo_shard", "weight": 2}]}, {"rolls": {"min": 0, "max": 10}, "entries": [{"type": "empty", "weight": 100}, {"type": "item", "name": "minecraft:iron_ingot", "functions": [{"function": "set_count", "count": {"min": 0, "max": 5}}], "weight": 10}, {"type": "item", "name": "minecraft:kelp", "functions": [{"function": "set_count", "count": {"min": 0, "max": 5}}], "weight": 4}, {"type": "item", "name": "minecraft:grass_block", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 5}, {"type": "item", "name": "minecraft:quartz", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 4}, {"type": "item", "name": "minecraft:slime_ball", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 4}, {"type": "item", "name": "minecraft:sapling", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}, {"function": "set_data", "data": {"min": 0, "max": 6}}], "weight": 10}, {"type": "item", "name": "minecraft:cherry_sapling", "functions": [{"function": "set_count", "count": {"min": 0, "max": 4}}], "weight": 10}, {"type": "item", "name": "minecraft:bamboo", "functions": [{"function": "set_count", "count": {"min": 0, "max": 2}}], "weight": 10}, {"type": "item", "name": "minecraft:mangrove_propagule", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 10}, {"type": "item", "name": "minecraft:gold_ingot", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 5}, {"type": "item", "name": "minecraft:redstone", "functions": [{"function": "set_count", "count": {"min": 4, "max": 9}}], "weight": 5}, {"type": "item", "name": "minecraft:dye", "functions": [{"function": "set_data", "data": {"min": 0, "max": 20}}, {"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 5}, {"type": "item", "name": "minecraft:diamond", "functions": [{"function": "set_count", "count": {"min": 0, "max": 5}}], "weight": 3}, {"type": "item", "name": "minecraft:coal", "functions": [{"function": "set_count", "count": {"min": 3, "max": 8}}], "weight": 10}, {"type": "item", "name": "minecraft:bread", "functions": [{"function": "set_count", "count": {"min": 0, "max": 8}}], "weight": 15}, {"type": "item", "name": "minecraft:melon_seeds", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 10}, {"type": "item", "name": "minecraft:pumpkin_seeds", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 10}, {"type": "item", "name": "minecraft:beetroot_seeds", "functions": [{"function": "set_count", "count": {"min": 0, "max": 4}}], "weight": 10}, {"type": "item", "name": "minecraft:potato", "functions": [{"function": "set_count", "count": {"min": 0, "max": 8}}], "weight": 10}, {"type": "item", "name": "minecraft:carrot", "functions": [{"function": "set_count", "count": {"min": 0, "max": 8}}], "weight": 10}, {"type": "item", "name": "minecraft:wheat", "functions": [{"function": "set_count", "count": {"min": 0, "max": 12}}], "weight": 15}, {"type": "item", "name": "minecraft:wheat_seeds", "functions": [{"function": "set_count", "count": {"min": 0, "max": 4}}], "weight": 5}, {"type": "item", "name": "minecraft:beetroot", "functions": [{"function": "set_count", "count": {"min": 0, "max": 8}}], "weight": 5}, {"type": "item", "name": "minecraft:wooden_hoe", "functions": [{"function": "set_count", "count": 1}], "weight": 1}, {"type": "item", "name": "minecraft:gunpowder", "functions": [{"function": "set_count", "count": {"min": 0, "max": 8}}], "weight": 10}, {"type": "item", "name": "minecraft:string", "functions": [{"function": "set_count", "count": {"min": 0, "max": 4}}], "weight": 10}, {"type": "item", "name": "minecraft:glowstone", "functions": [{"function": "set_count", "count": {"min": 0, "max": 2}}], "weight": 10}, {"type": "item", "name": "minecraft:sugar_cane", "functions": [{"function": "set_count", "count": {"min": 0, "max": 3}}], "weight": 10}, {"type": "item", "name": "minecraft:raw_iron", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}], "weight": 12}, {"type": "item", "name": "minecraft:iron_ingot", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}], "weight": 10}, {"type": "item", "name": "minecraft:raw_gold", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 7}, {"type": "item", "name": "minecraft:gold_ingot", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 6}]}, {"rolls": {"min": 0, "max": 3}, "entries": [{"type": "empty", "weight": 100}, {"type": "item", "name": "minecraft:iron_pickaxe", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:iron_sword", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:iron_chestplate", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:iron_helmet", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:iron_leggings", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:iron_boots", "functions": [{"function": "looting_enchant", "count": {"min": 0, "max": 1}}, {"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 10}, {"type": "item", "name": "minecraft:diamond_pickaxe", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:diamond_sword", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:diamond_chestplate", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:diamond_helmet", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:diamond_leggings", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:diamond_boots", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 3}, {"type": "item", "name": "minecraft:trident", "functions": [{"function": "enchant_random_gear", "chance": 0.35}, {"function": "set_damage", "damage": {"min": 0.2, "max": 0.8}}], "weight": 1}, {"type": "item", "name": "minecraft:enchanted_book", "functions": [{"function": "enchant_randomly"}], "weight": 2}]}, {"rolls": {"min": 0, "max": 8}, "entries": [{"type": "empty", "weight": 100}, {"type": "item", "name": "minecraft:rotten_flesh", "functions": [{"function": "set_count", "count": {"min": 1, "max": 8}}], "weight": 20}, {"type": "item", "name": "minecraft:bone", "functions": [{"function": "set_count", "count": {"min": 1, "max": 6}}], "weight": 15}, {"type": "item", "name": "minecraft:spider_eye", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}], "weight": 10}, {"type": "item", "name": "minecraft:string", "functions": [{"function": "set_count", "count": {"min": 1, "max": 6}}], "weight": 15}, {"type": "item", "name": "minecraft:gunpowder", "functions": [{"function": "set_count", "count": {"min": 1, "max": 5}}], "weight": 12}, {"type": "item", "name": "minecraft:slime_ball", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}], "weight": 8}, {"type": "item", "name": "minecraft:phantom_membrane", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 5}, {"type": "item", "name": "minecraft:blaze_rod", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 5}, {"type": "item", "name": "minecraft:blaze_powder", "functions": [{"function": "set_count", "count": {"min": 1, "max": 4}}], "weight": 8}, {"type": "item", "name": "minecraft:ghast_tear", "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}], "weight": 3}, {"type": "item", "name": "minecraft:magma_cream", "functions": [{"function": "set_count", "count": {"min": 1, "max": 3}}], "weight": 6}, {"type": "item", "name": "minecraft:ender_pearl", "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}], "weight": 4}, {"type": "item", "name": "minecraft:shulker_shell", "functions": [{"function": "set_count", "count": {"min": 1, "max": 2}}], "weight": 2}]}]}