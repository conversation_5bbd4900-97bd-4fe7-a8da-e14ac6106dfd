import { Block, BlockComponentPlayerBreakEvent, BlockPermutation } from "@minecraft/server";

/**
 * <PERSON>les custom grass block behavior including:
 * - Placement of tall grass when grass is placed
 * - Cleanup of connected grass blocks when destroyed
 */
export class GrassComponent {
  beforeOnPlayerPlace(ev: { block: Block; permutationToPlace: BlockPermutation; cancel: boolean }) {
    const block = ev.block;
    const above = block.above(1);
    const blockTypeId = ev.permutationToPlace.type.id;

    // Check if space above is not air
    if (!above?.isAir) {
      ev.cancel = true; // Cancel placement if blocked
      return;
    }

    // Place the top bit block above
    if (blockTypeId === "rza:tall_grass") above.setPermutation(BlockPermutation.resolve("rza:tall_grass", { "rza:top_bit": true }));
    else if (blockTypeId === "rza:large_fern") above.setPermutation(BlockPermutation.resolve("rza:large_fern", { "rza:top_bit": true }));
    return;
  }

  onPlayerDestroy(ev: BlockComponentPlayerBreakEvent) {
    const block = ev.block;
    const below = block.below(1);

    // Check if block below is tall grass and is the bottom part
    // @ts-ignore - Custom block state
    if (below?.permutation.getState("rza:top_bit") === false) {
      const loc = below.center();
      // Remove connected tall grass using setblock command
      below.dimension.runCommand(`setblock ${loc.x} ${loc.y} ${loc.z} air [] destroy`);
    }
    return;
  }
}
