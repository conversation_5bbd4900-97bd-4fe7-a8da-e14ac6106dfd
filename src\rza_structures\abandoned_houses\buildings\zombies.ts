/**
 * Zombie Spawning Module for Abandoned Houses
 *
 * This module handles the spawning of various zombie types around abandoned house structures.
 * It uses weighted random selection to determine zombie types and handles spawning with
 * proper error handling and logging.
 *
 * @module AbandonedHouses/Zombies
 */

import { Vector3, Dimension, world } from "@minecraft/server";
import { getRandomInt } from "../../utils/rng";
import { getRandomLocation } from "../../utils/vector3";

/**
 * Gets the available zombie types based on the current world day
 *
 * @returns {Array<{type: string, weight: number}>} Array of available zombie types and their weights
 */
function getZombieWeightsByDay(): Array<{ type: string; weight: number }> {
  const currentDay = world.getDay();
  const weights = [];

  // Walker zombie type - available from day 2
  if (currentDay >= 2) {
    weights.push({ type: "walker", weight: 50 });
  }

  // Unlock miner zombies after day 15
  if (currentDay >= 15) {
    weights.push({ type: "miner", weight: 20 });
  }

  // Unlock feral zombies after day 30
  if (currentDay >= 30) {
    weights.push({ type: "feral", weight: 15 });
  }

  // Unlock spitter zombies after day 50
  if (currentDay >= 50) {
    weights.push({ type: "spitter", weight: 15 });
  }

  return weights;
}

/**
 * Summons random weighted zombies for abandoned houses
 *
 * This function handles the spawning of zombies around a given location using weighted
 * random selection. It includes error handling and logging for spawn failures.
 *
 * @example
 * // Spawn zombies around a house at (100, 64, 200) in the overworld
 * const location = { x: 100, y: 64, z: 200 };
 * spawnZombies(location, world.getDimension("overworld"));
 *
 * @param {Vector3} location - The center location to spawn zombies around
 * @param {Dimension} dimension - The dimension to spawn zombies in
 * @returns {void}
 * @throws Will log warnings to console if spawning fails
 */
export function spawnZombies(location: Vector3, dimension: Dimension): void {
  // Spawn settings
  const baseOffset = 0;
  const additionalOffset = 3;
  const yOffset = 1;
  const minZombies = 1;
  const maxZombies = 4;

  try {
    const zombieCount = getRandomInt(minZombies, maxZombies);
    const availableZombies = getZombieWeightsByDay();

    // Only spawn zombies if there are available types
    if (availableZombies.length === 0) {
      return;
    }

    for (let i = 0; i < zombieCount; i++) {
      // Calculate the total weight
      const totalWeight = availableZombies.reduce((sum, { weight }) => sum + weight, 0);
      // Generate a random number between 0 and the total weight
      const randomNum = getRandomInt(0, totalWeight);
      // Find the zombie type based on the random number
      let cumulativeWeight = 0;
      let zombieType = "";

      // Iterate through zombie weights to find the selected type
      for (const { type, weight } of availableZombies) {
        cumulativeWeight += weight;

        // Check if the random number falls within this weight range
        if (randomNum <= cumulativeWeight) {
          zombieType = type;
          break;
        }
      }

      // Generate a random spawn position around the given location
      const spawnPos = getRandomLocation(location, dimension, baseOffset, additionalOffset, yOffset, true);

      // If a valid spawn position is found, attempt to summon the zombie
      if (spawnPos) {
        try {
          // Construct the command to summon the selected zombie type
          const command = `execute positioned ${spawnPos.x} ${spawnPos.y} ${spawnPos.z} run function abandoned_houses/summon_${zombieType}`;

          // Execute the command asynchronously
          dimension.runCommand(command);
        } catch (error) {
          console.warn(`Error executing summon command: ${error}`);
        }
      }
    }
  } catch (error) {
    // Handle any errors that occur during the spawning process
    console.warn(`Error spawning zombies: ${error}`);

    // If the error is an Error object, log the stack trace for debugging
    if (error instanceof Error) {
      console.warn(`Stack trace: ${error.stack}`);
    }
  }
}
