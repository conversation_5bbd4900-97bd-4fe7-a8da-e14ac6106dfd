{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:swamp_sweet_berry_bush.feature_rule", "places_feature": "rza:scatter_swamp_sweet_berry_bush.feature"}, "conditions": {"placement_pass": "surface_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "operator": "==", "value": "rza_swamp"}]}, "distribution": {"iterations": "(query.noise((variable.originx + 32) / 200.0, (variable.originz + 32) / 200.0) < -0.8) ? 16 : 32", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [0, "query.heightmap(variable.worldx, variable.worldz)"]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}