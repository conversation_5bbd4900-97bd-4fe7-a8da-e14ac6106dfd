{"format_version": "1.21.20", "minecraft:processor_list": {"description": {"identifier": "rza:illager_tower_designer"}, "processors": [{"processor_type": "minecraft:rule", "rules": [{"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:grass_block", "probability": 0.3}, "output_state": {"name": "minecraft:cracked_stone_bricks"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:grass_block", "probability": 0.2}, "output_state": {"name": "minecraft:cobblestone"}}, {"input_predicate": {"predicate_type": "minecraft:random_block_match", "block": "minecraft:grass_path", "probability": 0.2}, "output_state": {"name": "minecraft:stone_bricks"}}]}]}}