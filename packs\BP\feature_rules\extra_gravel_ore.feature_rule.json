{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:extra_gravel_ore.feature_rule", "places_feature": "minecraft:gravel_ore_feature"}, "conditions": {"placement_pass": "underground_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "value": "wasteland"}]}, "distribution": {"scatter_chance": {"numerator": 1, "denominator": 16}, "iterations": 80, "coordinate_eval_order": "zyx", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [-64, 50]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}