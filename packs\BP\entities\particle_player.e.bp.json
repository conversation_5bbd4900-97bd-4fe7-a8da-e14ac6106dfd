{
  // Particle Controller Entity
  // Manages environmental particle effects based on biome conditions
  "format_version": "1.21.70",
  "minecraft:entity": {
    "description": {
      "identifier": "rza:particle_player", // Entity ID for command usage
      "is_spawnable": false, // Prevent natural spawning
      "is_summonable": true, // Allow summon via commands/scripts
      "is_experimental": false,
      "properties": {
        "rza:effect_length": { "type": "int", "client_sync": false, "range": [0, 6000], "default": 0 },
        "rza:particle_active": { "type": "bool", "client_sync": false, "default": false },
        "rza:effect2_length": { "type": "int", "client_sync": false, "range": [0, 6000], "default": 0 }
      }
    },
    "component_groups": {},
    "components": {
      "minecraft:damage_sensor": { "triggers": [{ "cause": "all", "deals_damage": "no" }] },
      // Biome-based particle activation system
      "minecraft:environment_sensor": {
        "triggers": [
          // Activate particles when NOT in ocean biomes
          { "filters": { "none_of": [{ "test": "has_biome_tag", "value": "ocean", "subject": "self" }] }, "event": "rza:play_particles" },
          // Reset particle system when IN ocean biomes
          {
            "filters": {
              "any_of": [
                // Checks for any ocean biome tag variant
                { "test": "has_biome_tag", "value": "ocean", "subject": "self" }
              ]
            },
            "event": "rza:reset_in_ocean"
          }
        ]
      }
    },
    "events": {
      "rza:play_particles": {},
      "rza:reset_in_ocean": { "set_property": { "rza:effect_length": 0, "rza:effect2_length": 0, "rza:particle_active": false } }
    }
  }
}
