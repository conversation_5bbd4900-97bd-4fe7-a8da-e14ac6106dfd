// For workplace 4 - Cartographer's Building
{
  "pools": [
    {
      "rolls": { "min": 2, "max": 4 },
      "entries": [
        { "type": "empty", "weight": 40 },
        { "type": "item", "name": "minecraft:empty_map", "weight": 15, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:compass", "weight": 8 },
        { "type": "item", "name": "minecraft:filled_map", "weight": 3 },
        { "type": "item", "name": "minecraft:recovery_compass", "weight": 1 }
      ]
    },
    {
      "rolls": { "min": 3, "max": 6 },
      "entries": [
        { "type": "empty", "weight": 30 },
        { "type": "item", "name": "minecraft:paper", "weight": 15, "functions": [{ "function": "set_count", "count": { "min": 2, "max": 8 } }] },
        { "type": "item", "name": "minecraft:book", "weight": 8, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:feather", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 4 } }] },
        { "type": "item", "name": "minecraft:ink_sac", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] }
      ]
    },
    {
      "rolls": { "min": 1, "max": 3 },
      "entries": [
        { "type": "empty", "weight": 25 },
        { "type": "item", "name": "minecraft:emerald", "weight": 8, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 4 } }] },
        { "type": "item", "name": "minecraft:spyglass", "weight": 3 },
        {
          "type": "item",
          "name": "minecraft:glass_bottle",
          "weight": 8,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        { "type": "empty", "weight": 10 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 3 },
      "entries": [
        { "type": "empty", "weight": 35 },
        { "type": "item", "name": "minecraft:bread", "weight": 15, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 4 } }] },
        { "type": "item", "name": "minecraft:cookie", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 2, "max": 6 } }] },
        { "type": "item", "name": "minecraft:dried_kelp", "weight": 8, "functions": [{ "function": "set_count", "count": { "min": 2, "max": 6 } }] },
        { "type": "item", "name": "minecraft:apple", "weight": 8, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] }
      ]
    }
  ]
}
