{"format_version": "1.13.0", "minecraft:feature_rules": {"description": {"identifier": "rza:diamond_ore.feature_rule", "places_feature": "minecraft:diamond_ore_feature"}, "conditions": {"placement_pass": "underground_pass", "minecraft:biome_filter": [{"test": "has_biome_tag", "value": "wasteland"}]}, "distribution": {"iterations": 3, "coordinate_eval_order": "zyx", "x": {"distribution": "uniform", "extent": [0, 16]}, "y": {"distribution": "uniform", "extent": [-64, 16]}, "z": {"distribution": "uniform", "extent": [0, 16]}}}}