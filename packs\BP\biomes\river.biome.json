{"format_version": "1.21.70", "minecraft:biome": {"description": {"identifier": "river"}, "components": {"minecraft:climate": {"downfall": 0.5, "snow_accumulation": [0, 0.125], "temperature": 0.8}, "minecraft:overworld_height": {"noise_type": "river"}, "minecraft:surface_parameters": {"sea_floor_depth": 7, "sea_floor_material": "minecraft:gravel", "foundation_material": "minecraft:stone", "mid_material": "minecraft:dirt", "top_material": "minecraft:dirt", "sea_material": "minecraft:water"}, "minecraft:tags": {"tags": ["animal", "monster", "river"]}}}}