// Workplace 5 - Shepherd's Building
{
  "pools": [
    {
      "rolls": { "min": 1, "max": 3 },
      "entries": [
        { "type": "item", "name": "minecraft:white_wool", "weight": 20, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 6 } }] },
        { "type": "item", "name": "minecraft:black_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:gray_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:brown_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        {
          "type": "item",
          "name": "minecraft:light_gray_wool",
          "weight": 10,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        { "type": "item", "name": "minecraft:lime_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:cyan_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:pink_wool", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        {
          "type": "item",
          "name": "minecraft:purple_wool",
          "weight": 10,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        {
          "type": "item",
          "name": "minecraft:orange_wool",
          "weight": 10,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        { "type": "empty", "weight": 45 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:shears", "weight": 5 },
        { "type": "item", "name": "minecraft:string", "weight": 8, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 6 } }] },
        { "type": "item", "name": "minecraft:lead", "weight": 3, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 2 } }] },
        { "type": "empty", "weight": 40 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:white_dye", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:black_dye", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:red_dye", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:blue_dye", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:yellow_dye", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "empty", "weight": 35 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:emerald", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 4 } }] },
        { "type": "item", "name": "minecraft:bread", "weight": 15, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "empty", "weight": 35 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        {
          "type": "item",
          "name": "minecraft:white_carpet",
          "weight": 7,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 4 } }]
        },
        { "type": "item", "name": "minecraft:bed", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 2 } }] },
        { "type": "item", "name": "minecraft:painting", "weight": 6, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:gray_carpet", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        {
          "type": "item",
          "name": "minecraft:black_carpet",
          "weight": 5,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        { "type": "empty", "weight": 45 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:red_wool", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:blue_wool", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:yellow_wool", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "empty", "weight": 40 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:loom", "weight": 5 },
        { "type": "item", "name": "minecraft:name_tag", "weight": 3 },
        { "type": "item", "name": "minecraft:bone_meal", "weight": 10, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 6 } }] },
        { "type": "item", "name": "minecraft:flower_banner_pattern", "weight": 2 },
        { "type": "empty", "weight": 35 }
      ]
    },
    {
      "rolls": { "min": 1, "max": 2 },
      "entries": [
        { "type": "item", "name": "minecraft:red_carpet", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        { "type": "item", "name": "minecraft:blue_carpet", "weight": 5, "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }] },
        {
          "type": "item",
          "name": "minecraft:yellow_carpet",
          "weight": 5,
          "functions": [{ "function": "set_count", "count": { "min": 1, "max": 3 } }]
        },
        { "type": "empty", "weight": 40 }
      ]
    }
  ]
}
