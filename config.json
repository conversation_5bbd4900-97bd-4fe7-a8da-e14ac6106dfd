{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "RZA Worldgen Pack", "packs": {"behaviorPack": "./packs/BP", "resourcePack": "./packs/RP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"addon_builder": {"url": "github.com/Raboy-13/Regolith-Filters", "version": "1b90576a84a6e1cc385ec624fb502007fd9d21b8"}}, "formatVersion": "1.4.0", "profiles": {"build": {"export": {"readOnly": false, "target": "local"}, "filters": [{"filter": "addon_builder"}]}, "default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}}}}